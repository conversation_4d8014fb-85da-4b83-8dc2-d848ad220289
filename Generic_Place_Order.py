from Symphony import Symphony_PlaceOrder, Symphony_Get_Masterdf, Fyers_To_Symphony_Converter
from Fyers import Fyers_PlaceOrder
from config import logger

def unified_place_order(broker, option, side, qty, order_type):
    logger.info(f"Inside unified_place_order function: Placing order for {option['symbol']} with side {side} and qty {qty}")
    
    try:
        if broker.order_source == "fyers":
            if "symbol" not in option:
                raise ValueError("Fyers orders require 'symbol' in option dictionary")
            return Fyers_PlaceOrder.fyers_place_order(broker, option, side, qty, order_type)
            
        elif broker.order_source == "symphony":
            if "ExchangeInstrumentId" not in option:
                masterdf = Symphony_Get_Masterdf.get_masterdf(broker.market_symphony)
                exchange_instrument_id = Fyers_To_Symphony_Converter.get_exchangeInstrumentID(option, masterdf)
                if not exchange_instrument_id:
                    raise ValueError(f"Could not find matching instrument ID for {option['symbol']}")
                option["ExchangeInstrumentId"] = exchange_instrument_id
            return Symphony_PlaceOrder.symphony_place_order(broker, option, side, qty, order_type)
        
        else:
            raise ValueError(f"Invalid order source: {broker.order_source}")
            
    except Exception as e:
        logger.error(f"Error in unified_place_order: {e}")
        raise