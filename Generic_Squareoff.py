from config import logger, BrokerContext
from Generic_Place_Order import unified_place_order
from Get_Positions import get_positions

def squareoff_positions(broker, positions, resizefactor=None):
    """
    Generic function to square off all positions across different brokers.
    
    Args:
        broker: Broker instance (Fyers or Symphony)
        positions: Dictionary containing current positions
        resizefactor: Optional factor to resize the quantity (default: None)
    
    Returns:
        List of order statuses
    """
    if not positions:
        logger.info("Currently, there are no open positions to be squared off.")
        return []

    all_status = []

    # Iterate through each position type and its data
    for position_type, position_data in positions.items():
        # Handle case where position_data is a list (multiple positions)
        if isinstance(position_data, list):
            for position in position_data:
                qty = position.get("qty", 0)
                if qty > 0:
                    # Determine the action: 'buy' for short positions, 'sell' for long positions
                    action = "buy" if "SHORT" in position_type else "sell"
                    
                    # Apply resize factor if provided
                    if resizefactor:
                        qty = int(qty * resizefactor)
                    
                    logger.info(f"Placing {action} order for {qty} of {position.get('symbol')}")
                    all_status.append(unified_place_order(broker, position, action, qty, "limit"))

        # Handle case where position_data is a dict (single position)
        elif isinstance(position_data, dict) and position_data:  # Check if dict is not empty
            qty = position_data.get("qty", 0)
            if qty > 0:
                # Determine the action: 'buy' for short positions, 'sell' for long positions
                action = "buy" if "SHORT" in position_type else "sell"
                
                # Apply resize factor if provided
                if resizefactor:
                    qty = int(qty * resizefactor)
                
                logger.info(f"Placing {action} order for {qty} of {position_data.get('symbol')}")
                all_status.append(unified_place_order(broker, position_data, action, qty, "limit"))

    return all_status
#=----------------------------------------------------------------------------------------------------------------
#broker = BrokerContext()
#positions = get_positions(broker)
#squareoff_positions(broker, positions)