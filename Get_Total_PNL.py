from config import logger
from Fyers import Fyers_Get_Total_PNL
from Symphony import Symphony_Get_Total_PNL 
import os

def get_total_pnl(broker):
    """
    Unified function to get total PNL from either Fyers or Symphony.
    Returns standardized PNL format regardless of source.
    """
    try:
        if broker.order_source == "fyers":
            logger.info("Fetching PNL from Fyers")
            result = Fyers_Get_Total_PNL.get_total_pnl(broker.fyers)
            
        elif broker.order_source == "symphony":
            logger.info("Fetching PNL from Symphony")
            result = Symphony_Get_Total_PNL.symphony_get_total_pnl(broker, clientID=os.getenv("Interactive_clientID"))
            
        else:
            raise ValueError(f"Invalid order source: {broker.order_source}")
            
        return result
        
    except Exception as e:
        logger.error(f"Error fetching total PNL: {e}")
        raise