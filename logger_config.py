import logging
import os
from datetime import datetime

def setup_logger(name, log_file, level=logging.INFO):
    """Function to setup a logger with structured log directories."""
    # Get the directory of the current script
    script_dir = os.path.dirname(os.path.abspath(__file__))

    # Create logs directory if it doesn't exist
    logs_dir = os.path.join(script_dir, "logs")
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)

    # Get current date details
    today_date = datetime.now()
    month_folder = today_date.strftime('%B_%Y')
    day_file = today_date.strftime('%d-%m-%Y.log')

    # Create month directory if it doesn't exist
    month_dir = os.path.join(logs_dir, month_folder)
    if not os.path.exists(month_dir):
        os.makedirs(month_dir)

    # Construct the full path for the log file
    log_file_path = os.path.join(month_dir, day_file)

    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')

    handler = logging.FileHandler(log_file_path)
    handler.setFormatter(formatter)

    global logger
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Check if the logger already has handlers to avoid adding multiple handlers
    if not logger.handlers:
        logger.addHandler(handler)

    # Disable propagation to prevent duplicate logs
    logger.propagate = False

    return logger

def get_logger(name):
    """Function to get an existing logger by name."""
    return logging.getLogger(name)

# Example usage:
#logger = setup_logger('main_logger')
#same_logger = get_logger('main_logger')