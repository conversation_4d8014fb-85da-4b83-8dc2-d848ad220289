from config import BrokerContext,logger
from Get_Positions import get_positions
from DeltaCheck import delta_check
from datetime import datetime, date, timedelta
from DeltaAction import deltaAction
from Generic_Squareoff import squareoff_positions
from Fyers import Fyers_Tradebook, Fyers_save_final_pnl
import time, sys, random, os
from dotenv import load_dotenv
from Expiry_New_Recreate_Strategy import execute_BCS 
from Get_Trailing_TP_SL import get_targetprofit_stoploss

# Load environment variables from .env file
load_dotenv()

def get_randomized_times():
    """Calculate randomized start and exit times based on configuration."""
    # Seed random with today's date for consistency throughout the day
    today = date.today().isoformat()
    random.seed(today)
    
    # Get base times from environment
    start_base = datetime.strptime(os.getenv('START_BASE_TIME'), '%H:%M').time()
    exit_base = datetime.strptime(os.getenv('EXIT_BASE_TIME'), '%H:%M').time()
    
    # Get variation ranges
    start_variation = int(os.getenv('START_TIME_VARIATION', '10'))
    exit_variation = int(os.getenv('EXIT_TIME_VARIATION', '10'))
    
    # Calculate random offsets
    start_offset = random.randint(-start_variation, start_variation)
    exit_offset = random.randint(-exit_variation, exit_variation)
    
    # Apply offsets
    start_dt = datetime.combine(date.today(), start_base)
    exit_dt = datetime.combine(date.today(), exit_base)
    
    start_dt = start_dt + timedelta(minutes=start_offset)
    exit_dt = exit_dt + timedelta(minutes=exit_offset)
    
    #logger.info(f"Randomized start time: {start_dt.time()}, exit time: {exit_dt.time()}")
    return start_dt.time(), exit_dt.time()


def get_main_positions(positions):
    """Extract only the main four positions without extra buys"""
    def get_main_buy_position(buy_positions):
        """Helper function to get the main buy position (higher LTP)"""
        if not isinstance(buy_positions, list):
            return {}
        if not buy_positions:
            return {}
        # Sort by LTP in descending order and take the first one (highest LTP)
        sorted_positions = sorted(buy_positions, key=lambda x: float(x.get('ltp', 0)), reverse=True)
        return sorted_positions[0] if sorted_positions else {}

    main_positions = {
        "CE_SHORT": positions.get("CE_SHORT", {}),
        "PE_SHORT": positions.get("PE_SHORT", {}),
        "CE_BUY": get_main_buy_position(positions.get("CE_BUY", [])),
        "PE_BUY": get_main_buy_position(positions.get("PE_BUY", []))
    }
    return main_positions

def exit_trading(broker, all_positions, now, start_time, exit_time):
    squareoff_positions(broker, all_positions)  # Square off everything
    logger.info(f"Squared off all positions for today {now}. Time to save PNL and exit...")  
    Fyers_save_final_pnl.save_final_pnl(broker, start_time, exit_time) 
    logger.info(f"Today's {now}, PNL is saved. Exiting the script.")
    sys.exit()

def check_pnl(broker, all_positions, now, start_time, exit_time):
    """
    Check PNL conditions and square off positions if target profit or stop loss is hit.
    Returns True if exit conditions are met and positions are squared off.
    """
    try:
        total_pnl, targetprofit, stoploss = get_targetprofit_stoploss(broker, exit_time)
        if total_pnl is None:
            logger.error("Failed to fetch total PNL. Skipping PNL check.")
            return

        logger.info(f"Total PNL: {total_pnl}, Target Profit: {targetprofit}, Stop Loss: {stoploss}")

        if total_pnl <= stoploss or total_pnl >= targetprofit:
            logger.info("TargetProfit_StopLoss Exit conditions met. Proceeding to square off positions.")
            exit_trading(broker, all_positions, now, start_time, exit_time)                    
        else:
            logger.info("TargetProfit_StopLoss Exit conditions not met. Continuing to monitor.")
    except Exception as e:
        logger.error(f"An error occurred while checking PNL: {e}")

def check_prices(broker, start_time, exit_time):
    """Main process to check prices every minute."""
    logger.info(f"Starting the price-checking process in {broker.order_source}...")

    while True:
        try:
            now = datetime.now()          
             
            if now.second % 15 == 0:  # Trigger every 15 seconds
                logger.info("--------------------------------------------------------------------------------------")
                logger.info(f"Checking prices at {now.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # Get all positions including extra buys
                all_positions = get_positions(broker)
                logger.debug(f"All current positions: {all_positions}")
                
                # Get only main positions for delta checks
                main_positions = get_main_positions(all_positions)
                
               # Check PNL every minute
                check_pnl(broker, all_positions, now, start_time, exit_time)
                
                # At 3 PM, square off everything
                if now.time() >= exit_time:
                    logger.info(f"It's {now.time()}, past exit time {exit_time}. Square off all positions.")
                    exit_trading(broker, all_positions, now, start_time, exit_time)                    

                
                # Delta check and action only on main positions
                ret = delta_check(broker, main_positions,fixed_threshold=0.15)
                logger.debug(f"Delta check result: {ret}")
                              
                if ret:
                    logger.info(f"Delta condition met. Square off all positions.")
                    exit_trading(broker, all_positions, now, start_time, exit_time)                    
                else:
                    logger.info("Delta condition not met. Waiting for the next check.")
          
            time.sleep(1)  # Sleep for a second to avoid unnecessary loop iterations    
        
        except ValueError as e:
            logger.error(f"A value error occurred: {e}")
        except Exception as e:
            logger.error(f"An error occurred: {e}")

def main():
    try:
        logger.info("Starting the main process...")
        broker = BrokerContext()
        logger.info("Broker initialized successfully.")
        Fyers_Tradebook.tradebookinitialize()
        now = datetime.now().time()

        if not broker:
            logger.error("Broker initialization failed. Exiting.")
            return
         # Get randomized start and exit times for today
        start_time, exit_time = get_randomized_times()
        logger.info(f"Today's trading window: Start at {start_time}, Exit at {exit_time}")
        logger.info(f"Waiting for start time {start_time} to begin processing...")

        while True:
            now = datetime.now().time()
            logger.info(f"Current time: {now}")
            if now < start_time:
                time.sleep(60)  # Sleep for a minute before checking again
                continue

            # Only check start_time for BCS strategy execution
            logger.info("Checking positions...")    
            positions = get_positions(broker)

            if positions and any(positions.values()):
                logger.info("Positions already exist. Skipping BCS strategy execution.")
            else:
                execute_BCS(broker)
            
            time.sleep(1)  # Sleep for 1 second to avoid excessive CPU usage
            check_prices(broker, start_time, exit_time)
            time.sleep(1)  # Sleep for 1 second to avoid excessive CPU usage

    except Exception as e:
        logger.error(f"An error occurred in the main process: {e}", exc_info=True)

if __name__ == "__main__":
    main()