from config import logger
from Generic_Place_Order import unified_place_order
from Fyers import Fyers_Get_OTM_Options, Fyers_VIX_Threshold, Fyers_Utility 
import re, os, json
from Get_Positions import get_positions
from datetime import datetime
from Fyers.Fyers_Get_Wednesday_Half_Qty import get_half_qty
from dotenv import load_dotenv

load_dotenv()

def multiply_and_round_to_75(num, resizefactor):
    return round((num * resizefactor) / 75) * 75

def squareoff_positions(broker, positions, resizefactor=None):

    """
    Square off all positions. If no positions are open, log a message and exit gracefully.
    :param fyers: Fyers API client instance.
    :param positions: Dictionary containing current positions.
    :param resizefactor: Optional factor to resize the quantity.
    :return: List of order statuses.
    """
    # Check if there are any open positions
    if not any(positions.values()):
        logger.info("Currently, there are no open positions to be squared off.")
        return []

    vix = Fyers_VIX_Threshold.get_vix(broker.fyers)

    ce_buy = positions.get('CE_BUY', {})
    ce_short = positions.get('CE_SHORT', {})
    pe_buy = positions.get('PE_BUY', {})
    pe_short = positions.get('PE_SHORT', {})

    # Calculate quantities with optional resizing
    ce_short_qty = multiply_and_round_to_75(ce_short.get("qty", 0), resizefactor) if resizefactor else ce_short.get("qty", 0)
    pe_short_qty = multiply_and_round_to_75(pe_short.get("qty", 0), resizefactor) if resizefactor else pe_short.get("qty", 0)
    ce_buy_qty = multiply_and_round_to_75(ce_buy.get("qty", 0), resizefactor) if resizefactor else ce_buy.get("qty", 0)
    pe_buy_qty = multiply_and_round_to_75(pe_buy.get("qty", 0), resizefactor) if resizefactor else pe_buy.get("qty", 0)

    logger.info(f"Squaring off positions with quantities - CE Short: {ce_short_qty}, PE Short: {pe_short_qty}, CE Buy: {ce_buy_qty}, PE Buy: {pe_buy_qty}")

    all_status = []
    if ce_short_qty > 0:
        all_status.append(unified_place_order(broker, ce_short, "buy", ce_short_qty, "limit"))
    if pe_short_qty > 0:
        all_status.append(unified_place_order(broker, pe_short, "buy", pe_short_qty, "limit"))
    if ce_buy_qty > 0:
        all_status.append(unified_place_order(broker, ce_buy, "sell", ce_buy_qty, "limit"))
    if pe_buy_qty > 0:
        all_status.append(unified_place_order(broker, pe_buy, "sell", pe_buy_qty, "limit"))

    return all_status
    

def recreate_strategy(broker):
    """
    Recreate the balanced calendar spread strategy using updated sell option prices and calendar adjustments.
    """
    try:
        vix = Fyers_VIX_Threshold.get_vix(broker.fyers)
        if vix is None:
            return {"error": "Failed to fetch VIX value"}

        sell_option_price = Fyers_Utility.get_sell_option_price(vix)
        logger.info(f"Sell Option Price: {sell_option_price}")
        calender_price = Fyers_Utility.get_calender_diff_value(sell_option_price, vix)
        logger.info(f"Calender Price: {calender_price}")

        # Fetch and parse the JSON-encoded environment variable
        RESIZE_VALUES = json.loads(os.getenv('RESIZE_VALUES'))
        
        # Retrieve expiry_day from .env file
        expiry_day = os.getenv("Expiry_Day").upper()  # Example: "MONDAY"
        
        # Get the current weekday in uppercase
        current_weekday_name = datetime.today().strftime("%A").upper()
        
        #Hardcoding the value to FRIDAY for testing purpose
        #current_weekday_name = "FRIDAY"

        # Get the resize value from the dictionary
        resize = RESIZE_VALUES[expiry_day][current_weekday_name]
        
        logger.info(f"Resize value: {resize}")

        sell_option_price = sell_option_price * resize
        calender_price = calender_price * resize

        logger.info(f"Resized Sell Option Price: {sell_option_price}")
        logger.info(f"Resized Calculated Calendar Price: {calender_price}")
        
        nifty_otm_prices_current = Fyers_Get_OTM_Options.get_nifty_otm_options(broker.fyers, expiry_type="current", otm_count=int(os.getenv("otm_count")))
        nifty_otm_prices_next = Fyers_Get_OTM_Options.get_nifty_otm_options(broker.fyers, expiry_type="next", otm_count=int(os.getenv("otm_count")))
        
        sell_ce = Fyers_Utility.find_closest_price(sell_option_price, nifty_otm_prices_current["CE"])
        sell_pe = Fyers_Utility.find_closest_price(sell_option_price, nifty_otm_prices_current["PE"])
        buy_ce = Fyers_Utility.find_closest_price(calender_price, nifty_otm_prices_next["CE"])
        buy_pe = Fyers_Utility.find_closest_price(calender_price, nifty_otm_prices_next["PE"])
        
        logger.info(f"Recreating strategy with:")
        logger.info(f"Sell CE: {sell_ce}")
        logger.info(f"Sell PE: {sell_pe}")
        logger.info(f"Buy CE: {buy_ce}")
        logger.info(f"Buy PE: {buy_pe}")
        
        # Get quantities
        full_qty = int(os.getenv("GLOBAL_QTY"))
        
       # Check if it's expiry day
        is_expiry_day = current_weekday_name == expiry_day
        logger.info(f"Is it Expiry Day? {is_expiry_day}")
        qty = get_half_qty(full_qty) if is_expiry_day else full_qty
        logger.info(f"Using {'half' if is_expiry_day else 'full'} quantity: {qty}")
        
        unified_place_order(broker, buy_ce, "buy", qty, "limit")
        unified_place_order(broker, buy_pe, "buy", qty, "limit")
        unified_place_order(broker, sell_ce, "sell", qty, "limit")
        unified_place_order(broker, sell_pe, "sell", qty, "limit")
        
        return {"sell_ce": sell_ce, "sell_pe": sell_pe, "buy_ce": buy_ce, "buy_pe": buy_pe}
    except Exception as e:
        logger.error(f"Error in recreate_strategy: {str(e)}")
        return {"error": str(e)}


def deltaAdjustment(broker, positions):
    """
    Adjusts the delta by squaring off the profitable leg and re-establishing a new position.
    """
    logger.info(f"Current positions: {positions}")

    try:
        vix = Fyers_VIX_Threshold.get_vix(broker.fyers)
        if vix is None:
            return {"error": "Failed to fetch VIX value"}
        
        ce_short = positions["CE_SHORT"]
        pe_short = positions["PE_SHORT"]
        ce_buy = positions["CE_BUY"]
        pe_buy = positions["PE_BUY"]
        
        # Determine the lowest priced short option
        lowest_option = min([ce_short, pe_short], key=lambda x: x["ltp"])
        
        if lowest_option == ce_short:
            squareoff_leg, squareoff_calender = ce_short, ce_buy
            new_leg_price = pe_short["ltp"]
        else:
            squareoff_leg, squareoff_calender = pe_short, pe_buy
            new_leg_price = ce_short["ltp"]
        
        # Square off the profitable leg along with its calendar hedge
        unified_place_order(broker, squareoff_leg, "buy", squareoff_leg["qty"], "limit")
        unified_place_order(broker, squareoff_calender, "sell", squareoff_calender["qty"], "limit")
        
        # Establish a new leg with similar price as the opposite leg
        sell_option_price = new_leg_price
        calender_price = Fyers_Utility.get_calender_diff_value(sell_option_price, vix)
        
        nifty_otm_prices_current = Fyers_Get_OTM_Options.get_nifty_otm_options(broker.fyers, expiry_type="current", otm_count=int(os.getenv("otm_count")))
        nifty_otm_prices_next = Fyers_Get_OTM_Options.get_nifty_otm_options(broker.fyers, expiry_type="next", otm_count=int(os.getenv("otm_count")))
        
        new_sell_leg = Fyers_Utility.find_closest_price(sell_option_price, nifty_otm_prices_current["CE"] if lowest_option == ce_short else nifty_otm_prices_current["PE"])
        new_calender_leg = Fyers_Utility.find_closest_price(calender_price, nifty_otm_prices_next["CE"] if lowest_option == ce_short else nifty_otm_prices_next["PE"])
        
        # Place new orders
        unified_place_order(broker, new_calender_leg, "buy", squareoff_calender["qty"], "limit")
        unified_place_order(broker, new_sell_leg, "sell", squareoff_leg["qty"], "limit")
        
        return {"new_sell_leg": new_sell_leg, "new_calender_leg": new_calender_leg}
    except Exception as e:
        logger.error(f"Error in deltaAdjustment: {e}")
        return {"error": str(e)}


def deltaAction(broker, positions):
    logger.info(f"Positons in DeltaAction:, {positions}")

    """
    Perform necessary actions based on delta neutrality conditions.
    :param positions: Dictionary containing CE Sell, CE Buy, PE Sell, PE Buy with symbols and LTP.
    """
    try:
        match = re.search(r"\d{5}(?=CE)", positions["CE_SHORT"]["symbol"])
        sell_ce_strike = int(match.group(0)) if match else None    
        #sell_ce_strike = 23550   #--Testing purpose
        match = re.search(r"\d{5}(?=PE)", positions["PE_SHORT"]["symbol"])
        sell_pe_strike = int(match.group(0)) if match else None    
        atm_strike = Fyers_Utility.get_nifty_atm_strike(broker.fyers)  # Approximate ATM
        #atm_strike = 23550       #--Testing purpose


        # Check if either sell leg becomes ATM or ITM
        if sell_ce_strike <= atm_strike or sell_pe_strike >= atm_strike:
            logger.info("Delta condition met, exiting all positions and recreating strategy.")
            status = squareoff_positions(broker, positions)
            status = recreate_strategy(broker)
        else:
            logger.info("Delta condition met but not ATM/ITM, adjusting position.")
            status = deltaAdjustment(broker, positions)

    except Exception as e:
        logger.error(f"Error in deltaAction: {str(e)}")
        logger.error(f"Error in deltaAction: {e}")

#---------------------------------------------------------------------------------------------------------------
#broker = BrokerContext()
#positions = get_positions(broker)
#deltaAction(broker, positions)
#deltaAdjustment(broker, positions)
#squareoff_positions(broker, positions)
#recreate_strategy(broker)