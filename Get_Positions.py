from datetime import datetime
import os, json
from config import logger
from Fyers import Fyers_Get_Positions, <PERSON>yers_Thursday_Get_Positions
from Symphony import Symphony_Get_Positions, Symphony_Thursday_Get_Positions
from dotenv import load_dotenv

load_dotenv()

def get_positions(broker):
    """
    Unified function to get positions from either Fyers or Symphony.
    Returns standardized position format regardless of source.
    """
    try:
        # Retrieve expiry_day from .env file
        expiry_day = os.getenv("Expiry_Day").upper()  # Example: "MONDAY"     
             
        today_day = datetime.now().strftime("%A").upper()

        #Hardcoding the value to FRIDAY for testing purpose
        #today_day = "FRIDAY" 

        if broker.order_source == "fyers":
            logger.info("Fetching positions from Fyers")
            if today_day == expiry_day:
                logger.info(f"Fetching positions from Fyers for {expiry_day}")
                result = Fyers_Thursday_Get_Positions.thursday_get_positions(broker.fyers)
            else:
                result = Fyers_Get_Positions.get_positions(broker.fyers)
                
        elif broker.order_source == "symphony":
            logger.info("Fetching positions from Symphony")
            client_id = os.getenv("Interactive_clientID")
            if today_day == expiry_day:
                logger.info(f"Fetching positions from Symphony for {expiry_day}")
                result = Symphony_Thursday_Get_Positions.thursday_get_positions(broker, clientID=client_id)
            else:
                result = Symphony_Get_Positions.get_positions_xts(broker, clientID=client_id)
                
        else:
            raise ValueError(f"Invalid order source: {broker.order_source}")
            
        return result
        
    except Exception as e:
        logger.error(f"Error fetching positions: {e}")
        raise