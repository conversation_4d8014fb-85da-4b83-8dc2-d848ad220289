import os
from dotenv import load_dotenv
from logger_config import get_logger
from Get_Total_PNL import get_total_pnl
from datetime import datetime, timedelta, time

# Initialize logger
logger = get_logger('main_logger')

# Load environment variables (like capital) from .env
load_dotenv()

# === Global Variables ===
sl_at_2_45 = None              # SL captured exactly at 2:44 PM
max_sl_after_245 = None        # Tracks the highest SL since 2:45 PM
current_sl = -1.25              # Current SL used for comparison
current_tp = 1.0               # Current TP
initial_sl = -1.25
initial_tp = 2.0


# === STEP 1: Capture SL exactly at 2:44 PM ===
def capture_sl_at_2_44(sl):
    global sl_at_2_45

    current_time = datetime.now()
    ref_time = datetime.combine(current_time.date(), datetime.strptime("14:44", "%H:%M").time())

    # Allow tolerance of ±15 seconds
    if sl_at_2_45 is None and abs((current_time - ref_time).total_seconds()) <= 15:
        sl_at_2_45 = sl
        logger.info(f"[Captured SL @ 2:45 PM] sl_at_2_45 = {sl_at_2_45:.2f}%")


# === STEP 2: Adjust SL after 2:45 PM (only tighten it) ===
def adjusted_sl(total_pnl, sl, exit_time):
    global sl_at_2_45, max_sl_after_245, current_sl

    current_time = datetime.now()
    ref_time = datetime.combine(current_time.date(), datetime.strptime("14:45", "%H:%M").time())
    exit_time = datetime.combine(current_time.date(), exit_time)

    if total_pnl >= 0.10 and current_time >= ref_time:
        if sl_at_2_45 is None:
            sl_at_2_45 = sl  # fallback just in case
        
        # === NEW: Tighter SL if total_pnl > 0.20 ===
        if total_pnl >= 0.20:
            step_minutes = 2.5   # Faster tightening (every 2.5 minutes)
            step_multiplier = 1.5  # Optional: Increase step size to tighten faster*
        else:
            step_minutes = 5
            step_multiplier = 1.0

        total_steps = int((exit_time - ref_time).total_seconds() / (step_minutes * 60))
        logger.info(f"total steps after 2:45 PM:{total_steps}")

        #step_minutes = 5

        elapsed_minutes = (current_time - ref_time).total_seconds() / 60
        elapsed_steps = int(elapsed_minutes // step_minutes)
        elapsed_steps = min(elapsed_steps, total_steps - 1)

        step_size = max(0, ((total_pnl / total_steps) * step_multiplier))
        new_sl = step_size * elapsed_steps
        new_sl = max(sl_at_2_45, new_sl)
        new_sl = min(new_sl, total_pnl)

        # Enforce minimum SL floor of 0.1 if PnL is positive
        if new_sl < 0.1:
            logger.info(f"[SL Floor] Adjusted SL too low at {new_sl:.4f}, raising to 0.1")
            new_sl = 0.1

        # Update max_sl_after_245 only if higher
        if max_sl_after_245 is None:
            max_sl_after_245 = new_sl
        else:
            max_sl_after_245 = max(max_sl_after_245, new_sl)

        # Extra guard: never return a lower SL
        if current_sl is not None and current_sl > max_sl_after_245:
            logger.info(f"[SL Guard] Prevented SL drop after 2:45 PM: {current_sl:.4f} > {max_sl_after_245:.4f}")
            return current_sl

        logger.info(f"[Adjusted SL after 2:45] New: {new_sl:.4f}, Max SL: {max_sl_after_245:.4f}")
        return max_sl_after_245

    else:
        return sl  # Before 2:45 PM or total_pnl < 0.1


# === STEP 3: Determine SL/TP based on PNL ===
def get_trailing_levels(total_pnl, exit_time):
    global current_sl, current_tp

    # Initialize sl and tp with default values
    sl, tp = -1.25, 1.0

    # Adjust SL and TP based on rising PNL, but avoid reducing SL after 2:45 PM
    now_time = datetime.now().time()
    cutoff_time = datetime.strptime("14:45", "%H:%M").time()

    if total_pnl >= 2.0:
        sl, tp = 1.5, 3.0
    elif total_pnl >= 1.5 and current_sl < 1.5:
        sl, tp = 1.0, 2.5
    elif total_pnl >= 1.0 and current_sl < 1.0:
        sl, tp = 0.5, 2.0
    elif total_pnl >= 0.75 and current_sl < 0.75:
        sl, tp = 0.25, 1.5
    elif total_pnl >= 0.5 and current_sl < 0.25:
        sl, tp = 0.1, 1.25
    elif total_pnl < 0.5 and current_sl < 0.1 and now_time < cutoff_time:
        sl, tp = -1.25, 1.0  # Initial values

    # Capture SL at 2:44 PM
    capture_sl_at_2_44(sl)

    # Apply adjusted SL logic after 2:45 PM
    sl = adjusted_sl(total_pnl, sl, exit_time)

    # Final guard: SL must never go down after 2:45 PM
    if now_time >= cutoff_time:
        sl = max(current_sl, sl)
        current_sl = sl  # Only allow increasing or same
    else:
        current_sl = sl  # Before 2:45 PM, allow reset

    current_tp = tp
    return sl, tp
  

# === STEP 4: Final function to get absolute values for SL and TP ===
def get_targetprofit_stoploss(broker, exit_time):
    """
    This function fetches the PNL, calculates the current SL/TP %,
    and then returns the absolute stop loss and target profit amounts.
    """
    # load the capital from the .env file
    capital = float(os.getenv("Capital")) 
    logger.info(f"Capital: {capital}")
    
    # Fetch current PNL and calculate percentage
    total_pnl, open_pnl, closed_pnl = get_total_pnl(broker)

    total_pnl_percentage = (total_pnl / capital) * 100
    
    # Get updated SL and TP %
    sl_percentage, tp_percentage = get_trailing_levels(total_pnl_percentage, exit_time)
    
    # Calculate absolute values
    target_profit = capital * (tp_percentage / 100)
    stop_loss = capital * (sl_percentage / 100)
    
    logger.info(f"Current PNL: {total_pnl}, PNL%: {total_pnl_percentage:.2f}%, SL%: {sl_percentage}, TP%: {tp_percentage}")
    logger.info(f"Target Profit: {target_profit}, Stop Loss: {stop_loss}")    
    
    return total_pnl, target_profit, stop_loss

