from config import logger, <PERSON><PERSON>r<PERSON>ontext
from Generic_Place_Order import unified_place_order
from dotenv import load_dotenv
from Fyers import Fyers_Get_OTM_Options 
from Fyers import Fyers_VIX_Threshold
from Fyers import Fyers_Utility 
from Fyers import Fyers_Tradebook 
from Fyers.Fyers_Get_Wednesday_Half_Qty import get_half_qty
import os, json
from datetime import datetime

load_dotenv()

def execute_BCS(broker):
    """
    Recreate the balanced calendar spread strategy with additional buys around Rs 1
    and half quantity for specific positions.
    """
    try:
        vix = Fyers_VIX_Threshold.get_vix(broker.fyers)
        logger.info(f"Fetched VIX value: {vix}")
        if vix is None:
            return {"error": "Failed to fetch VIX value"}

        sell_option_price = Fyers_Utility.get_sell_option_price(vix)
        logger.info(f"Sell Option Price: {sell_option_price}")
        calender_price = Fyers_Utility.get_calender_diff_value(sell_option_price, vix)
        logger.info(f"Calender Price: {calender_price}")

        # Fetch and parse the JSON-encoded environment variable
        RESIZE_VALUES = json.loads(os.getenv('RESIZE_VALUES'))
        
        # Retrieve expiry_day from .env file
        expiry_day = os.getenv("Expiry_Day").upper()  # Example: "MONDAY"
        
        # Get the current weekday in uppercase
        current_weekday_name = datetime.today().strftime("%A").upper()
        
        # Get the resize value from the dictionary
        resize = RESIZE_VALUES[expiry_day][current_weekday_name]
        
        logger.info(f"Resize value: {resize}")

        sell_option_price = sell_option_price * resize
        calender_price = calender_price * resize

        logger.info(f"Resized Sell Option Price: {sell_option_price}")
        logger.info(f"Resized Calculated Calendar Price: {calender_price}")

        # Get OTM options for current and next expiry
        nifty_otm_prices_current = Fyers_Get_OTM_Options.get_nifty_otm_options(broker.fyers, expiry_type="current", otm_count=int(os.getenv("otm_count")))
        logger.info(f"Nifty OTM Prices Current: {nifty_otm_prices_current}")
        nifty_otm_prices_next = Fyers_Get_OTM_Options.get_nifty_otm_options(broker.fyers, expiry_type="next", otm_count=int(os.getenv("otm_count")))
        logger.info(f"Nifty OTM Prices Next: {nifty_otm_prices_next}")

        # Find options closest to target prices
        sell_ce = Fyers_Utility.find_closest_price(sell_option_price, nifty_otm_prices_current["CE"])
        sell_pe = Fyers_Utility.find_closest_price(sell_option_price, nifty_otm_prices_current["PE"])
        buy_ce = Fyers_Utility.find_closest_price(calender_price, nifty_otm_prices_next["CE"])
        buy_pe = Fyers_Utility.find_closest_price(calender_price, nifty_otm_prices_next["PE"])
        
        # Find additional buy options around Rs 1
        extra_buy_ce = Fyers_Utility.find_closest_price(1.0, nifty_otm_prices_current["CE"])
        extra_buy_pe = Fyers_Utility.find_closest_price(1.0, nifty_otm_prices_current["PE"])
        
        logger.info(f"Recreating Expiry Day BCS strategy with:")
        logger.info(f"Extra Buy CE (Rs1): {extra_buy_ce}")
        logger.info(f"Extra Buy PE (Rs1): {extra_buy_pe}")
        logger.info(f"Buy CE: {buy_ce}")
        logger.info(f"Buy PE: {buy_pe}")
        logger.info(f"Sell CE: {sell_ce}")
        logger.info(f"Sell PE: {sell_pe}")
        
        
        # Get quantities
        full_qty = int(os.getenv("GLOBAL_QTY"))
        half_qty = get_half_qty(full_qty)
        logger.info(f"Full Quantity: {full_qty}, Half Quantity: {half_qty}")

        # Place orders

        # Additional buy orders with half quantity
        unified_place_order(broker, extra_buy_ce, "buy", half_qty, "limit")
        unified_place_order(broker, extra_buy_pe, "buy", half_qty, "limit")
        
        # Main strategy orders with full quantity
        unified_place_order(broker, buy_ce, "buy", half_qty, "limit")
        unified_place_order(broker, buy_pe, "buy", half_qty, "limit")
        unified_place_order(broker, sell_ce, "sell", half_qty, "limit")
        unified_place_order(broker, sell_pe, "sell", half_qty, "limit")
        
       
        return {
            "sell_ce": sell_ce, 
            "sell_pe": sell_pe, 
            "buy_ce": buy_ce, 
            "buy_pe": buy_pe,
            "extra_buy_ce": extra_buy_ce,
            "extra_buy_pe": extra_buy_pe
        }
    except Exception as e:
        logger.error(f"Error in Expiry_New_Recreate_Strategy: {e}")
        return {"error": str(e)}