from config import logger
from dotenv import load_dotenv
from Fyers import Fyers_Get_OTM_Options 
from Fyers import Fyers_VIX_Threshold
from Fyers import Fyers_Utility 
from Generic_Place_Order import unified_place_order
import os, json
from datetime import datetime

# Load environment variables from .env file
load_dotenv()

def execute_BCS(broker):
    logger.info(f"Executing BCS strategy with {broker.order_source}")

    try:
      
        vix = Fyers_VIX_Threshold.get_vix(broker.fyers)
        if vix is None:
            return {"error": "Failed to fetch VIX"}

        logger.info(f"Fetched VIX value: {vix}")

        sell_option_price = Fyers_Utility.get_sell_option_price(vix)
        logger.info(f"Sell Option Price: {sell_option_price}")
        calender_price = Fyers_Utility.get_calender_diff_value(sell_option_price, vix)
        logger.info(f"Calender Price: {calender_price}")

        # Parse the JSON-encoded environment variable
        resize_values_str = os.getenv('RESIZE_VALUES', '')

        if not resize_values_str:
            logger.error("RESIZE_VALUES environment variable is missing or empty.")
            return {"error": "RESIZE_VALUES environment variable is not set correctly"}

        try:
            RESIZE_VALUES = json.loads(resize_values_str)
            logger.info(f"Parsed RESIZE_VALUES successfully: {RESIZE_VALUES}")
        except json.JSONDecodeError as json_err:
            logger.error(f"JSON decoding failed for RESIZE_VALUES: {json_err}")
            return {"error": "Invalid RESIZE_VALUES format in environment variable"}
        
        # Retrieve expiry_day from .env file
        expiry_day = os.getenv("Expiry_Day").upper()  # Example: "MONDAY"
        logger.info(f"Expiry Day: {expiry_day}")

        if not expiry_day:
            raise ValueError("Expiry_Day environment variable is not set")
        
        # Get the current weekday in uppercase
        current_weekday_name = datetime.today().strftime("%A").upper() 

        #hardcoding the value to FRIDAY for testing purpose
        #current_weekday_name = "FRIDAY"   

        logger.info(f"Current Weekday: {current_weekday_name}")
        
        # Get the resize value from the dictionary
        resize = RESIZE_VALUES[expiry_day][current_weekday_name]
        
        logger.info(f"Resize value: {resize}")

        sell_option_price = sell_option_price * resize
        calender_price = calender_price * resize

        logger.info(f"Resized Sell Option Price: {sell_option_price}")
        logger.info(f"Resized Calculated Calendar Price: {calender_price}")

        nifty_otm_prices_current = Fyers_Get_OTM_Options.get_nifty_otm_options(broker.fyers, expiry_type="current", otm_count=int(os.getenv("otm_count")))
        nifty_otm_prices_next = Fyers_Get_OTM_Options.get_nifty_otm_options(broker.fyers, expiry_type="next", otm_count=int(os.getenv("otm_count")))
        
        logger.info(f"Nifty OTM Prices Current: {nifty_otm_prices_current}")
        logger.info(f"Nifty OTM Prices Next: {nifty_otm_prices_next}")

        sell_ce = Fyers_Utility.find_closest_price(sell_option_price, nifty_otm_prices_current["CE"])
        sell_pe = Fyers_Utility.find_closest_price(sell_option_price, nifty_otm_prices_current["PE"])
        buy_ce = Fyers_Utility.find_closest_price(calender_price, nifty_otm_prices_next["CE"])
        buy_pe = Fyers_Utility.find_closest_price(calender_price, nifty_otm_prices_next["PE"])
        
        logger.info(f"Closest Sell CE: {sell_ce}")
        logger.info(f"Closest Sell PE: {sell_pe}")
        logger.info(f"Closest Buy CE: {buy_ce}")
        logger.info(f"Closest Buy PE: {buy_pe}")
        
        qty = int(os.getenv("GLOBAL_QTY"))
        logger.info(f"Order Quantity: {qty}")

        unified_place_order(broker, buy_ce, "buy", qty, "limit")
        unified_place_order(broker, buy_pe, "buy", qty, "limit")
        unified_place_order(broker, sell_ce, "sell", qty, "limit")
        unified_place_order(broker, sell_pe, "sell", qty, "limit")

        logger.info(F"Strategy recreation completed successfully for {broker.order_source} broker")
        return {"sell_ce": sell_ce, "sell_pe": sell_pe, "buy_ce": buy_ce, "buy_pe": buy_pe}
    except Exception as e:
        logger.error(f"Error in strategy recreation: {e}")
        return {"error": str(e)}
    
