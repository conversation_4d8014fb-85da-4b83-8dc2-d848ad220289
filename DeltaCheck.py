from Fyers import Fyers_VIX_Threshold 
import logger_config 

#Get logger
logger = logger_config.get_logger('main_logger')
#logger = logger_config.setup_logger('main_logger', 'main.log')


def delta_check(broker, positions, fixed_threshold= None):
    """
    Extracts CE_SHORT and PE_SHORT LTPs, calculates the ratio of lower LTP to higher LTP, and compares with the threshold.
    :return: Boolean indicating if the ratio is below the threshold.
    """
    logger.info(f"Positons in DeltaCheck:{positions}")
    try:
        ce_short = positions["CE_SHORT"]
        pe_short = positions["PE_SHORT"]
        
        if not ce_short or not pe_short:
            return False
       
        ce_short_price = float(ce_short["ltp"])
        pe_short_price = float(pe_short["ltp"])

        # Calculate the ratio of lower LTP to higher LTP
        lower_ltp = min(ce_short_price, pe_short_price)
        higher_ltp = max(ce_short_price, pe_short_price)
        ratio = round(lower_ltp / higher_ltp, 2) if higher_ltp != 0 else None
        logger.info(f"Ratio of Lower LTP to higher LTP:{ratio}")
        
        # Get VIX and threshold
        vix = Fyers_VIX_Threshold.get_vix(broker.fyers)
        if vix is None:
            return False
        
        threshold = Fyers_VIX_Threshold.get_threshold(vix, fixed_threshold)
        logger.info(f"Threshold:{threshold}")

        if ratio < threshold:
            return True
        else: 
            return False
    
    except Exception as e:
        logger.error(f"An error occurred:{e}")
        return False
 
#-----------------------------------------------------------------------------------------------
#fyers = initialize_fyers()

#positions = get_positions(fyers)
#Example Usage
#result = delta_check(fyers, positions)
#if result:
#    print("Delta check condition met!")
#else:
#   print("Delta check condition not met or failed.")