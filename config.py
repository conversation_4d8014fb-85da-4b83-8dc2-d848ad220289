# config.py
import os
from dotenv import load_dotenv
from Fyers import AccessTokenFromRefreshToken
from Fyers import Fyers_Utility
from Symphony import Symphony_Utility
import logger_config
from datetime import datetime

load_dotenv()

logger = logger_config.setup_logger('main_logger', 'main.log')
logger = logger_config.get_logger('main_logger')

ORDER_SOURCE = os.getenv("ORDER_SOURCE", "fyers").lower()

class BrokerContext:
    def __init__(self):
    # Check if access token needs to be updated
        last_token_update = os.getenv('LAST_TOKEN_UPDATE')
        today = datetime.now().strftime('%Y-%m-%d')
        
        # Flag to track token update
        token_updated_today = False 

        if not last_token_update or last_token_update != today:
            logger.info("Access token needs to be updated for today")
            AccessTokenFromRefreshToken.get_access_token()
            # Reload environment variables to get the updated token
            load_dotenv()
            token_updated_today = True
            # Update the environment variable to prevent multiple updates in the same day
            os.environ['LAST_TOKEN_UPDATE'] = today        
                 
        self.fyers = Fyers_Utility.initialize_fyers()

        self.market_symphony = None
        self.interactive_symphony = None
        self.order_source = ORDER_SOURCE

        if self.order_source == "symphony":
            try:    
                self.market_symphony = Symphony_Utility.initialize_market_symphony()
                self.interactive_symphony = Symphony_Utility.initialize_interactive_symphony()

                if not self.market_symphony or not self.interactive_symphony:
                    logger.error("Failed to initialize Symphony connections")
                    raise Exception("Symphony initialization failed")
                
                # Verify successful login
                if not self.interactive_symphony.token:
                    raise Exception("Symphony initialization succeeded but no token received")
                
                #logger.info("Symphony initialization successful")
            except Exception as e:
                logger.error(f"Symphony initialization failed: {str(e)}")
                raise